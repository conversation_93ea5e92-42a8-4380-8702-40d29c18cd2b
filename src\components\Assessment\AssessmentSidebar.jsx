import React from 'react';
import { getCompletionStatus, getCategoryCompletion } from './utils/scoreCalculator';

const AssessmentSidebar = ({
  assessments,
  currentAssessment,
  currentCategory,
  assessmentData,
  onAssessmentChange,
  onCategoryChange
}) => {
  const completionStatus = getCompletionStatus(assessmentData);

  const getAssessmentIcon = (assessmentType) => {
    switch (assessmentType) {
      case 'via':
        return '🎯';
      case 'riasec':
        return '🔧';
      case 'bigFive':
        return '🧠';
      default:
        return '📋';
    }
  };

  const getCompletionColor = (percentage) => {
    if (percentage === 100) return 'text-green-600';
    if (percentage > 0) return 'text-yellow-600';
    return 'text-gray-400';
  };

  const getCompletionBg = (percentage) => {
    if (percentage === 100) return 'bg-green-100';
    if (percentage > 0) return 'bg-yellow-100';
    return 'bg-gray-100';
  };

  return (
    <div className="w-80 bg-white shadow-lg border-r border-gray-200 h-screen overflow-y-auto">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Assessment Navigation</h2>
        <p className="text-sm text-gray-600 mt-1">
          Complete all sections to submit your assessment
        </p>
      </div>

      <div className="p-4 space-y-4">
        {Object.entries(assessments).map(([assessmentKey, assessment]) => {
          const isCurrentAssessment = currentAssessment === assessmentKey;
          const status = completionStatus[assessmentKey];
          
          return (
            <div key={assessmentKey} className="space-y-2">
              {/* Assessment Header */}
              <button
                onClick={() => onAssessmentChange(assessmentKey)}
                className={`w-full text-left p-3 rounded-lg border transition-colors ${
                  isCurrentAssessment
                    ? 'border-indigo-300 bg-indigo-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getAssessmentIcon(assessmentKey)}</span>
                    <div>
                      <h3 className={`font-medium ${
                        isCurrentAssessment ? 'text-indigo-900' : 'text-gray-900'
                      }`}>
                        {assessment.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {status.completed} / {status.total} questions
                      </p>
                    </div>
                  </div>
                  <div className={`text-sm font-medium ${getCompletionColor(status.percentage)}`}>
                    {Math.round(status.percentage)}%
                  </div>
                </div>
                
                {/* Progress bar */}
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      status.percentage === 100 ? 'bg-green-500' :
                      status.percentage > 0 ? 'bg-yellow-500' : 'bg-gray-300'
                    }`}
                    style={{ width: `${status.percentage}%` }}
                  />
                </div>
              </button>

              {/* Categories */}
              {isCurrentAssessment && (
                <div className="ml-4 space-y-1">
                  {assessment.categories.map((categoryKey) => {
                    const categoryData = assessment.data.categories[categoryKey];
                    const isCurrentCategory = currentCategory === categoryKey;
                    const categoryCompletion = getCategoryCompletion(
                      assessmentData, 
                      assessmentKey, 
                      categoryKey
                    );
                    
                    return (
                      <button
                        key={categoryKey}
                        onClick={() => onCategoryChange(categoryKey)}
                        className={`w-full text-left p-2 rounded-md text-sm transition-colors ${
                          isCurrentCategory
                            ? 'bg-indigo-100 text-indigo-900 border border-indigo-200'
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{categoryData.name}</span>
                          <div className="flex items-center space-x-2">
                            <span className={`text-xs ${getCompletionColor(categoryCompletion.percentage)}`}>
                              {categoryCompletion.completed}/{categoryCompletion.total}
                            </span>
                            {categoryCompletion.percentage === 100 && (
                              <span className="text-green-600 text-xs">✓</span>
                            )}
                          </div>
                        </div>
                        
                        {/* Category progress bar */}
                        <div className="mt-1 w-full bg-gray-200 rounded-full h-1">
                          <div
                            className={`h-1 rounded-full transition-all duration-300 ${
                              categoryCompletion.percentage === 100 ? 'bg-green-500' :
                              categoryCompletion.percentage > 0 ? 'bg-yellow-500' : 'bg-gray-300'
                            }`}
                            style={{ width: `${categoryCompletion.percentage}%` }}
                          />
                        </div>
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Overall Progress Summary */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <h4 className="font-medium text-gray-900 mb-3">Overall Progress</h4>
        <div className="space-y-2">
          {Object.entries(completionStatus).map(([key, status]) => (
            <div key={key} className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                {assessments[key].title.split(' ')[0]}
              </span>
              <span className={`font-medium ${getCompletionColor(status.percentage)}`}>
                {Math.round(status.percentage)}%
              </span>
            </div>
          ))}
        </div>
        
        {/* Total progress */}
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm font-medium">
            <span className="text-gray-900">Total Progress</span>
            <span className={getCompletionColor(
              (completionStatus.via.percentage + 
               completionStatus.riasec.percentage + 
               completionStatus.bigFive.percentage) / 3
            )}>
              {Math.round(
                (completionStatus.via.percentage + 
                 completionStatus.riasec.percentage + 
                 completionStatus.bigFive.percentage) / 3
              )}%
            </span>
          </div>
          <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
            <div
              className="h-2 rounded-full bg-indigo-500 transition-all duration-300"
              style={{ 
                width: `${(completionStatus.via.percentage + 
                          completionStatus.riasec.percentage + 
                          completionStatus.bigFive.percentage) / 3}%` 
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentSidebar;
