import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../../data/assessmentQuestions';

/**
 * Convert 1-5 scale to 0-100 scale
 */
const scaleToPercent = (value) => {
  return Math.round((value - 1) * 25);
};

/**
 * Convert 1-5 scale to 0-100 scale for reverse-scored items
 */
const reverseScaleToPercent = (value) => {
  return Math.round((5 - value) * 25);
};

/**
 * Calculate average score for a set of questions
 */
const calculateAverage = (values) => {
  if (values.length === 0) return 0;
  const sum = values.reduce((acc, val) => acc + val, 0);
  return Math.round(sum / values.length);
};

/**
 * Get total number of questions across all assessments
 */
export const getTotalQuestions = () => {
  const viaTotal = Object.values(viaQuestions.categories).reduce(
    (sum, category) => sum + category.questions.length, 0
  );
  const riasecTotal = Object.values(riasecQuestions.categories).reduce(
    (sum, category) => sum + category.questions.length, 0
  );
  const bigFiveTotal = Object.values(bigFiveQuestions.categories).reduce(
    (sum, category) => sum + category.questions.length + (category.reverseQuestions?.length || 0), 0
  );
  
  return viaTotal + riasecTotal + bigFiveTotal;
};

/**
 * Get number of answered questions
 */
export const getAnsweredQuestions = (assessmentData) => {
  let count = 0;

  // Count VIA answers
  if (assessmentData.via) {
    Object.keys(viaQuestions.categories).forEach(category => {
      const categoryAnswers = assessmentData.via[category] || {};
      count += Object.keys(categoryAnswers).length;
    });
  }

  // Count RIASEC answers
  if (assessmentData.riasec) {
    Object.keys(riasecQuestions.categories).forEach(category => {
      const categoryAnswers = assessmentData.riasec[category] || {};
      count += Object.keys(categoryAnswers).length;
    });
  }

  // Count Big Five answers
  if (assessmentData.bigFive) {
    Object.keys(bigFiveQuestions.categories).forEach(category => {
      const categoryAnswers = assessmentData.bigFive[category] || {};
      count += Object.keys(categoryAnswers).length;
    });
  }

  return count;
};

/**
 * Calculate VIA-IS scores (24 character strengths)
 */
const calculateViaScores = (viaData) => {
  const scores = {};

  // Return empty scores if no data
  if (!viaData) {
    return {
      creativity: 0, curiosity: 0, judgment: 0, loveOfLearning: 0, perspective: 0,
      bravery: 0, perseverance: 0, honesty: 0, zest: 0,
      love: 0, kindness: 0, socialIntelligence: 0,
      teamwork: 0, fairness: 0, leadership: 0,
      forgiveness: 0, humility: 0, prudence: 0, selfRegulation: 0,
      appreciationOfBeauty: 0, gratitude: 0, hope: 0, humor: 0, spirituality: 0
    };
  }

  // VIA Character Strengths mapping
  const strengthsMapping = {
    wisdomAndKnowledge: [
      { key: 'creativity', start: 0, count: 4 },
      { key: 'curiosity', start: 4, count: 4 },
      { key: 'judgment', start: 8, count: 4 },
      { key: 'loveOfLearning', start: 12, count: 4 },
      { key: 'perspective', start: 16, count: 4 }
    ],
    courage: [
      { key: 'bravery', start: 0, count: 4 },
      { key: 'perseverance', start: 4, count: 4 },
      { key: 'honesty', start: 8, count: 4 },
      { key: 'zest', start: 12, count: 4 }
    ],
    humanity: [
      { key: 'love', start: 0, count: 4 },
      { key: 'kindness', start: 4, count: 4 },
      { key: 'socialIntelligence', start: 8, count: 4 }
    ],
    justice: [
      { key: 'teamwork', start: 0, count: 4 },
      { key: 'fairness', start: 4, count: 4 },
      { key: 'leadership', start: 8, count: 4 }
    ],
    temperance: [
      { key: 'forgiveness', start: 0, count: 4 },
      { key: 'humility', start: 4, count: 4 },
      { key: 'prudence', start: 8, count: 4 },
      { key: 'selfRegulation', start: 12, count: 4 }
    ],
    transcendence: [
      { key: 'appreciationOfBeauty', start: 0, count: 4 },
      { key: 'gratitude', start: 4, count: 4 },
      { key: 'hope', start: 8, count: 4 },
      { key: 'humor', start: 12, count: 4 },
      { key: 'spirituality', start: 16, count: 4 }
    ]
  };

  Object.entries(strengthsMapping).forEach(([category, strengths]) => {
    const categoryAnswers = viaData[category] || {};
    
    strengths.forEach(({ key, start, count }) => {
      const values = [];
      for (let i = start; i < start + count; i++) {
        if (categoryAnswers[i] !== undefined) {
          values.push(scaleToPercent(categoryAnswers[i]));
        }
      }
      scores[key] = values.length === count ? calculateAverage(values) : 0;
    });
  });

  return scores;
};

/**
 * Calculate RIASEC scores (6 dimensions)
 */
const calculateRiasecScores = (riasecData) => {
  const scores = {};

  // Return empty scores if no data
  if (!riasecData) {
    return {
      realistic: 0,
      investigative: 0,
      artistic: 0,
      social: 0,
      enterprising: 0,
      conventional: 0
    };
  }

  Object.entries(riasecQuestions.categories).forEach(([category, categoryData]) => {
    const categoryAnswers = riasecData[category] || {};
    const values = [];

    categoryData.questions.forEach((_, index) => {
      if (categoryAnswers[index] !== undefined) {
        values.push(scaleToPercent(categoryAnswers[index]));
      }
    });

    scores[category] = values.length === categoryData.questions.length ? calculateAverage(values) : 0;
  });

  return scores;
};

/**
 * Calculate Big Five (OCEAN) scores (5 dimensions)
 */
const calculateOceanScores = (bigFiveData) => {
  const scores = {};

  // Return empty scores if no data
  if (!bigFiveData) {
    return {
      openness: 0,
      conscientiousness: 0,
      extraversion: 0,
      agreeableness: 0,
      neuroticism: 0
    };
  }

  Object.entries(bigFiveQuestions.categories).forEach(([category, categoryData]) => {
    const categoryAnswers = bigFiveData[category] || {};
    const values = [];

    // Regular questions
    categoryData.questions.forEach((_, index) => {
      if (categoryAnswers[index] !== undefined) {
        values.push(scaleToPercent(categoryAnswers[index]));
      }
    });

    // Reverse-scored questions
    if (categoryData.reverseQuestions) {
      categoryData.reverseQuestions.forEach((_, index) => {
        const reverseIndex = categoryData.questions.length + index;
        if (categoryAnswers[reverseIndex] !== undefined) {
          values.push(reverseScaleToPercent(categoryAnswers[reverseIndex]));
        }
      });
    }

    const totalQuestions = categoryData.questions.length + (categoryData.reverseQuestions?.length || 0);
    scores[category] = values.length === totalQuestions ? calculateAverage(values) : 0;
  });

  return scores;
};

/**
 * Calculate all assessment scores
 */
export const calculateScores = (assessmentData) => {
  return {
    viaIs: calculateViaScores(assessmentData.via),
    riasec: calculateRiasecScores(assessmentData.riasec),
    ocean: calculateOceanScores(assessmentData.bigFive)
  };
};

/**
 * Get completion status for each assessment
 */
export const getCompletionStatus = (assessmentData) => {
  const viaTotal = Object.values(viaQuestions.categories).reduce(
    (sum, category) => sum + category.questions.length, 0
  );
  const riasecTotal = Object.values(riasecQuestions.categories).reduce(
    (sum, category) => sum + category.questions.length, 0
  );
  const bigFiveTotal = Object.values(bigFiveQuestions.categories).reduce(
    (sum, category) => sum + category.questions.length + (category.reverseQuestions?.length || 0), 0
  );

  let viaAnswered = 0;
  if (assessmentData.via) {
    Object.keys(viaQuestions.categories).forEach(category => {
      const categoryAnswers = assessmentData.via[category] || {};
      viaAnswered += Object.keys(categoryAnswers).length;
    });
  }

  let riasecAnswered = 0;
  if (assessmentData.riasec) {
    Object.keys(riasecQuestions.categories).forEach(category => {
      const categoryAnswers = assessmentData.riasec[category] || {};
      riasecAnswered += Object.keys(categoryAnswers).length;
    });
  }

  let bigFiveAnswered = 0;
  if (assessmentData.bigFive) {
    Object.keys(bigFiveQuestions.categories).forEach(category => {
      const categoryAnswers = assessmentData.bigFive[category] || {};
      bigFiveAnswered += Object.keys(categoryAnswers).length;
    });
  }

  return {
    via: { completed: viaAnswered, total: viaTotal, percentage: (viaAnswered / viaTotal) * 100 },
    riasec: { completed: riasecAnswered, total: riasecTotal, percentage: (riasecAnswered / riasecTotal) * 100 },
    bigFive: { completed: bigFiveAnswered, total: bigFiveTotal, percentage: (bigFiveAnswered / bigFiveTotal) * 100 }
  };
};

/**
 * Get category completion status
 */
export const getCategoryCompletion = (assessmentData, assessmentType, category) => {
  const assessmentConfig = {
    via: viaQuestions,
    riasec: riasecQuestions,
    bigFive: bigFiveQuestions
  };

  const categoryData = assessmentConfig[assessmentType].categories[category];
  if (!categoryData) return { completed: 0, total: 0, percentage: 0 };

  const totalQuestions = categoryData.questions.length + (categoryData.reverseQuestions?.length || 0);
  const categoryAnswers = (assessmentData[assessmentType] && assessmentData[assessmentType][category]) || {};
  const completed = Object.keys(categoryAnswers).length;

  return {
    completed,
    total: totalQuestions,
    percentage: (completed / totalQuestions) * 100
  };
};
