import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pollCount, setPollCount] = useState(0);

  // Poll for status updates
  useEffect(() => {
    if (!jobId) {
      setError('No job ID provided');
      setLoading(false);
      return;
    }

    const pollStatus = async () => {
      try {
        const response = await apiService.getAssessmentStatus(jobId);
        setStatus(response.data);
        
        if (response.data.status === 'completed') {
          // Navigate to results page
          setTimeout(() => {
            navigate(`/results/${response.data.resultId}`);
          }, 2000);
        } else if (response.data.status === 'failed') {
          setError('Assessment processing failed. Please try submitting again.');
        }
      } catch (error) {
        console.error('Status check error:', error);
        setError(error.message || 'Failed to check assessment status');
      } finally {
        setLoading(false);
      }
    };

    // Initial status check
    pollStatus();

    // Set up polling interval (every 5 seconds)
    const interval = setInterval(() => {
      setPollCount(prev => prev + 1);
      if (status?.status !== 'completed' && status?.status !== 'failed') {
        pollStatus();
      }
    }, 5000);

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [jobId, navigate, status?.status]);

  const getStatusIcon = (statusType) => {
    switch (statusType) {
      case 'queued':
        return (
          <div className="animate-pulse">
            <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'processing':
        return (
          <div className="animate-spin">
            <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
        );
      case 'completed':
        return (
          <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <LoadingSpinner size="lg" />
        );
    }
  };

  const getStatusMessage = (statusType) => {
    switch (statusType) {
      case 'queued':
        return {
          title: 'Assessment Queued',
          description: 'Your assessment is in the queue and will be processed shortly.',
          color: 'yellow'
        };
      case 'processing':
        return {
          title: 'Processing Assessment',
          description: 'Our AI is analyzing your responses and generating your personalized report.',
          color: 'blue'
        };
      case 'completed':
        return {
          title: 'Assessment Complete!',
          description: 'Your talent mapping report is ready. Redirecting to results...',
          color: 'green'
        };
      case 'failed':
        return {
          title: 'Processing Failed',
          description: 'There was an error processing your assessment. Please try again.',
          color: 'red'
        };
      default:
        return {
          title: 'Checking Status',
          description: 'Please wait while we check your assessment status.',
          color: 'gray'
        };
    }
  };

  if (loading && !status) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
          <LoadingSpinner size="lg" />
          <h2 className="text-xl font-semibold text-gray-900 mt-4">
            Checking Assessment Status
          </h2>
          <p className="text-gray-600 mt-2">
            Please wait while we retrieve your assessment status...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
          <ErrorMessage message={error} />
          <div className="mt-6 space-y-3">
            <button
              onClick={() => navigate('/assessment')}
              className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Take New Assessment
            </button>
            <button
              onClick={() => navigate('/dashboard')}
              className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusMessage(status?.status);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-lg">
        {/* Status Icon */}
        <div className="flex justify-center mb-6">
          {getStatusIcon(status?.status)}
        </div>

        {/* Status Title */}
        <h2 className={`text-2xl font-bold mb-4 text-${statusInfo.color}-600`}>
          {statusInfo.title}
        </h2>

        {/* Status Description */}
        <p className="text-gray-600 mb-6">
          {statusInfo.description}
        </p>

        {/* Status Details */}
        {status && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-medium text-gray-900 mb-3">Assessment Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Job ID:</span>
                <span className="font-mono text-gray-900">{jobId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium text-${statusInfo.color}-600 capitalize`}>
                  {status.status}
                </span>
              </div>
              {status.estimatedProcessingTime && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Est. Processing Time:</span>
                  <span className="text-gray-900">{status.estimatedProcessingTime}</span>
                </div>
              )}
              {status.queuePosition && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Queue Position:</span>
                  <span className="text-gray-900">{status.queuePosition}</span>
                </div>
              )}
              {status.tokenCost && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Token Cost:</span>
                  <span className="text-gray-900">{status.tokenCost}</span>
                </div>
              )}
              {status.remainingTokens !== undefined && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Remaining Tokens:</span>
                  <span className="text-gray-900">{status.remainingTokens}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        {(status?.status === 'queued' || status?.status === 'processing') && (
          <div className="mb-6">
            <div className="flex justify-center items-center space-x-2 text-sm text-gray-600">
              <LoadingSpinner size="sm" />
              <span>Checking status... ({pollCount} checks)</span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          {status?.status === 'completed' && (
            <button
              onClick={() => navigate(`/results/${status.resultId}`)}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              View Results
            </button>
          )}
          
          {status?.status === 'failed' && (
            <button
              onClick={() => navigate('/assessment')}
              className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Try Again
            </button>
          )}
          
          <button
            onClick={() => navigate('/dashboard')}
            className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssessmentStatus;
