import React from 'react';

const QuickNavigation = ({
  totalQuestions,
  currentQuestion,
  answers,
  onQuestionChange
}) => {
  // Calculate grid layout - 5 columns, rows as needed
  const columns = 5;
  const rows = Math.ceil(totalQuestions / columns);

  // Create grid of question numbers
  const questionGrid = [];
  for (let row = 0; row < rows; row++) {
    const rowQuestions = [];
    for (let col = 0; col < columns; col++) {
      const questionIndex = row * columns + col;
      if (questionIndex < totalQuestions) {
        rowQuestions.push(questionIndex);
      } else {
        rowQuestions.push(null); // Empty cell
      }
    }
    questionGrid.push(rowQuestions);
  }

  const getQuestionStatus = (questionIndex) => {
    if (questionIndex === currentQuestion) return 'current';
    if (answers[questionIndex] !== undefined) return 'answered';
    return 'unanswered';
  };

  const getQuestionStyles = (status) => {
    switch (status) {
      case 'current':
        return 'bg-indigo-600 text-white border-indigo-600 ring-2 ring-indigo-300 ring-offset-2';
      case 'answered':
        return 'bg-green-500 text-white border-green-500 hover:bg-green-600';
      case 'unanswered':
        return 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50 hover:border-gray-400';
      default:
        return 'bg-white text-gray-600 border-gray-300';
    }
  };

  const getQuestionIcon = (status, questionIndex) => {
    switch (status) {
      case 'current':
        return questionIndex + 1;
      case 'answered':
        return '✓';
      case 'unanswered':
        return questionIndex + 1;
      default:
        return questionIndex + 1;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Quick Navigation</h3>
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 rounded border"></div>
            <span className="text-gray-600">Answered</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-indigo-600 rounded border"></div>
            <span className="text-gray-600">Current</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-white border border-gray-300 rounded"></div>
            <span className="text-gray-600">Unanswered</span>
          </div>
        </div>
      </div>

      {/* Question Grid */}
      <div className="space-y-2">
        {questionGrid.map((row, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-5 gap-2">
            {row.map((questionIndex, colIndex) => {
              if (questionIndex === null) {
                return <div key={colIndex} className="w-12 h-12"></div>; // Empty cell
              }

              const status = getQuestionStatus(questionIndex);
              const styles = getQuestionStyles(status);
              const icon = getQuestionIcon(status, questionIndex);

              return (
                <button
                  key={questionIndex}
                  onClick={() => onQuestionChange(questionIndex)}
                  className={`w-12 h-12 rounded-lg border-2 font-medium text-sm transition-all duration-200 flex items-center justify-center ${styles}`}
                  title={`Question ${questionIndex + 1}${
                    answers[questionIndex] ? ` (answered: ${answers[questionIndex]})` : ''
                  }`}
                >
                  {icon}
                </button>
              );
            })}
          </div>
        ))}
      </div>

      {/* Progress Summary */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Progress: {Object.keys(answers).length} of {totalQuestions} questions
          </span>
          <span className="font-medium text-gray-900">
            {Math.round((Object.keys(answers).length / totalQuestions) * 100)}% Complete
          </span>
        </div>
        
        {/* Progress bar */}
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(Object.keys(answers).length / totalQuestions) * 100}%` }}
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex space-x-2">
          <button
            onClick={() => {
              // Go to first unanswered question
              for (let i = 0; i < totalQuestions; i++) {
                if (answers[i] === undefined) {
                  onQuestionChange(i);
                  break;
                }
              }
            }}
            className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
            disabled={Object.keys(answers).length === totalQuestions}
          >
            Go to first unanswered
          </button>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => onQuestionChange(0)}
            className="text-sm text-gray-600 hover:text-gray-800"
          >
            First
          </button>
          <span className="text-gray-300">|</span>
          <button
            onClick={() => onQuestionChange(totalQuestions - 1)}
            className="text-sm text-gray-600 hover:text-gray-800"
          >
            Last
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuickNavigation;
