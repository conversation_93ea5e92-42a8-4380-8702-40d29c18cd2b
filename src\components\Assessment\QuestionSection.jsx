import React from 'react';
import QuickNavigation from './QuickNavigation';

const QuestionSection = ({
  assessment,
  category,
  categoryData,
  scale,
  currentQuestion,
  answers,
  onAnswerSelect,
  onQuestionChange
}) => {
  // Get all questions for this category (including reverse questions for Big Five)
  const getAllQuestions = () => {
    const questions = [...categoryData.questions];
    if (categoryData.reverseQuestions) {
      questions.push(...categoryData.reverseQuestions);
    }
    return questions;
  };

  const allQuestions = getAllQuestions();
  const totalQuestions = allQuestions.length;
  const currentQuestionText = allQuestions[currentQuestion];
  const isReverseQuestion = assessment === 'bigFive' && 
    categoryData.reverseQuestions && 
    currentQuestion >= categoryData.questions.length;

  // Navigation handlers
  const goToNext = () => {
    if (currentQuestion < totalQuestions - 1) {
      onQuestionChange(currentQuestion + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestion > 0) {
      onQuestionChange(currentQuestion - 1);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key >= '1' && event.key <= '5') {
      const value = parseInt(event.key);
      onAnswerSelect(currentQuestion, value);
    } else if (event.key === 'ArrowLeft') {
      goToPrevious();
    } else if (event.key === 'ArrowRight') {
      goToNext();
    }
  };

  // Auto-advance to next question after answering
  const handleAnswerSelect = (value) => {
    onAnswerSelect(currentQuestion, value);
    
    // Auto-advance after a short delay
    setTimeout(() => {
      if (currentQuestion < totalQuestions - 1) {
        goToNext();
      }
    }, 300);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6" onKeyDown={handleKeyPress} tabIndex={0}>
      {/* Quick Navigation */}
      <QuickNavigation
        totalQuestions={totalQuestions}
        currentQuestion={currentQuestion}
        answers={answers}
        onQuestionChange={onQuestionChange}
      />

      {/* Question Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        {/* Question Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-3 py-1 rounded-full">
                Question {currentQuestion + 1} of {totalQuestions}
              </span>
              {isReverseQuestion && (
                <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                  Reverse Scored
                </span>
              )}
            </div>
            <div className="text-sm text-gray-500">
              Category: {categoryData.name}
            </div>
          </div>
          
          {/* Progress bar for current category */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestion + 1) / totalQuestions) * 100}%` }}
            />
          </div>
        </div>

        {/* Question Text */}
        <div className="mb-8">
          <h3 className="text-xl font-medium text-gray-900 leading-relaxed">
            {currentQuestionText}
          </h3>
          {isReverseQuestion && (
            <p className="text-sm text-yellow-700 mt-2 bg-yellow-50 p-2 rounded">
              Note: This question is reverse-scored. Consider how much this statement does NOT describe you.
            </p>
          )}
        </div>

        {/* Answer Options */}
        <div className="space-y-3">
          <p className="text-sm font-medium text-gray-700 mb-4">
            How much does this statement describe you?
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
            {scale.map((option) => {
              const isSelected = answers[currentQuestion] === option.value;
              
              return (
                <button
                  key={option.value}
                  onClick={() => handleAnswerSelect(option.value)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 text-center ${
                    isSelected
                      ? 'border-indigo-500 bg-indigo-50 text-indigo-900'
                      : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className={`text-2xl font-bold mb-2 ${
                    isSelected ? 'text-indigo-600' : 'text-gray-400'
                  }`}>
                    {option.value}
                  </div>
                  <div className="text-sm font-medium">
                    {option.label}
                  </div>
                </button>
              );
            })}
          </div>
          
          {/* Keyboard shortcuts hint */}
          <div className="text-center text-xs text-gray-500 mt-4">
            Tip: Use number keys 1-5 to answer quickly, or arrow keys to navigate
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={goToPrevious}
          disabled={currentQuestion === 0}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            currentQuestion === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <span>Previous</span>
        </button>

        <div className="text-sm text-gray-500">
          {Object.keys(answers).length} of {totalQuestions} answered
        </div>

        <button
          onClick={goToNext}
          disabled={currentQuestion === totalQuestions - 1}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            currentQuestion === totalQuestions - 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
          }`}
        >
          <span>Next</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Answer Summary */}
      {Object.keys(answers).length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Your Answers for {categoryData.name}</h4>
          <div className="grid grid-cols-10 gap-1">
            {Array.from({ length: totalQuestions }, (_, index) => (
              <div
                key={index}
                className={`w-8 h-8 rounded text-xs font-medium flex items-center justify-center cursor-pointer transition-colors ${
                  answers[index] !== undefined
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                } ${
                  index === currentQuestion ? 'ring-2 ring-indigo-500' : ''
                }`}
                onClick={() => onQuestionChange(index)}
                title={`Question ${index + 1}${answers[index] ? `: ${answers[index]}` : ' (unanswered)'}`}
              >
                {answers[index] || '?'}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionSection;
