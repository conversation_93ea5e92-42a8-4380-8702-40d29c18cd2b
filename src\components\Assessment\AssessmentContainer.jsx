import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AssessmentSidebar from './AssessmentSidebar';
import QuestionSection from './QuestionSection';
import AssessmentProgress from './AssessmentProgress';
import SubmissionModal from './SubmissionModal';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';
import { calculateScores, getTotalQuestions, getAnsweredQuestions } from './utils/scoreCalculator';
import apiService from '../../services/apiService';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';

const AssessmentContainer = () => {
  const navigate = useNavigate();
  
  // Assessment data structure
  const [assessmentData, setAssessmentData] = useState(() => {
    const saved = localStorage.getItem('assessmentData');
    return saved ? JSON.parse(saved) : {
      via: {},
      riasec: {},
      bigFive: {}
    };
  });

  // Current navigation state
  const [currentAssessment, setCurrentAssessment] = useState('via');
  const [currentCategory, setCurrentCategory] = useState('wisdomAndKnowledge');
  const [currentQuestion, setCurrentQuestion] = useState(0);

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [error, setError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Assessment configurations
  const assessments = {
    via: {
      title: 'VIA Character Strengths',
      data: viaQuestions,
      categories: Object.keys(viaQuestions.categories)
    },
    riasec: {
      title: 'RIASEC Holland Codes',
      data: riasecQuestions,
      categories: Object.keys(riasecQuestions.categories)
    },
    bigFive: {
      title: 'Big Five Inventory',
      data: bigFiveQuestions,
      categories: Object.keys(bigFiveQuestions.categories)
    }
  };

  // Save to localStorage whenever assessmentData changes
  useEffect(() => {
    localStorage.setItem('assessmentData', JSON.stringify(assessmentData));
  }, [assessmentData]);

  // Initialize current category when assessment changes
  useEffect(() => {
    const categories = assessments[currentAssessment].categories;
    if (!categories.includes(currentCategory)) {
      setCurrentCategory(categories[0]);
      setCurrentQuestion(0);
    }
  }, [currentAssessment]);

  // Handle answer selection
  const handleAnswerSelect = (questionIndex, value) => {
    setAssessmentData(prev => ({
      ...prev,
      [currentAssessment]: {
        ...prev[currentAssessment],
        [currentCategory]: {
          ...prev[currentAssessment][currentCategory],
          [questionIndex]: value
        }
      }
    }));
  };

  // Navigation handlers
  const handleAssessmentChange = (assessment) => {
    setCurrentAssessment(assessment);
    setCurrentQuestion(0);
    setError(null);
  };

  const handleCategoryChange = (category) => {
    setCurrentCategory(category);
    setCurrentQuestion(0);
    setError(null);
  };

  const handleQuestionChange = (questionIndex) => {
    setCurrentQuestion(questionIndex);
  };

  // Calculate progress
  const totalQuestions = getTotalQuestions();
  const answeredQuestions = getAnsweredQuestions(assessmentData);
  const progressPercentage = (answeredQuestions / totalQuestions) * 100;
  const isComplete = answeredQuestions === totalQuestions;

  // Handle submission
  const handleSubmit = async () => {
    if (!isComplete) {
      setError('Please complete all questions before submitting.');
      return;
    }

    setShowSubmissionModal(true);
  };

  const confirmSubmission = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const scores = calculateScores(assessmentData);
      
      const submissionData = {
        assessmentName: 'AI-Driven Talent Mapping',
        riasec: scores.riasec,
        ocean: scores.ocean,
        viaIs: scores.viaIs
      };

      const response = await apiService.submitAssessment(submissionData);
      
      if (response.success) {
        setSubmitSuccess(true);
        // Clear localStorage after successful submission
        localStorage.removeItem('assessmentData');
        
        // Navigate to status page after a short delay
        setTimeout(() => {
          navigate(`/assessment/status/${response.data.jobId}`);
        }, 2000);
      }
    } catch (error) {
      console.error('Submission error:', error);
      setError(error.message || 'Failed to submit assessment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentAssessmentData = assessments[currentAssessment];
  const currentCategoryData = currentAssessmentData.data.categories[currentCategory];

  if (submitSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
          <div className="text-green-600 text-6xl mb-4">✓</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Assessment Submitted!</h2>
          <p className="text-gray-600 mb-4">
            Your assessment has been successfully submitted for AI analysis.
          </p>
          <p className="text-sm text-gray-500">
            Redirecting to status page...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <AssessmentSidebar
          assessments={assessments}
          currentAssessment={currentAssessment}
          currentCategory={currentCategory}
          assessmentData={assessmentData}
          onAssessmentChange={handleAssessmentChange}
          onCategoryChange={handleCategoryChange}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="bg-white shadow-sm border-b border-gray-200 p-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentAssessmentData.title}
                </h1>
                <p className="text-gray-600 mt-1">
                  {currentCategoryData.name}
                </p>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">
                  Progress: {answeredQuestions} / {totalQuestions}
                </div>
                <AssessmentProgress 
                  progress={progressPercentage}
                  showPercentage={true}
                />
              </div>
            </div>
          </div>

          {/* Question Section */}
          <div className="flex-1 p-6">
            <QuestionSection
              assessment={currentAssessment}
              category={currentCategory}
              categoryData={currentCategoryData}
              scale={currentAssessmentData.data.scale}
              currentQuestion={currentQuestion}
              answers={assessmentData[currentAssessment][currentCategory] || {}}
              onAnswerSelect={handleAnswerSelect}
              onQuestionChange={handleQuestionChange}
            />
          </div>

          {/* Footer */}
          <div className="bg-white border-t border-gray-200 p-6">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {isComplete ? (
                  <span className="text-green-600 font-medium">
                    ✓ All questions completed
                  </span>
                ) : (
                  <span>
                    {totalQuestions - answeredQuestions} questions remaining
                  </span>
                )}
              </div>
              
              {error && (
                <ErrorMessage message={error} onClose={() => setError(null)} />
              )}
              
              <button
                onClick={handleSubmit}
                disabled={!isComplete || isSubmitting}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  isComplete && !isSubmitting
                    ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2">Submitting...</span>
                  </div>
                ) : (
                  'Submit Assessment'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Submission Modal */}
      {showSubmissionModal && (
        <SubmissionModal
          onConfirm={confirmSubmission}
          onCancel={() => setShowSubmissionModal(false)}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  );
};

export default AssessmentContainer;
